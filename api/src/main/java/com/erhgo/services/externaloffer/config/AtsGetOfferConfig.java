package com.erhgo.services.externaloffer.config;


import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.externaloffer.ExternalOffer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class AtsGetOfferConfig implements Cloneable {

    /**
     * Url of remote offers to fetch
     */
    private String remoteUrl;
    /**
     * Maximum number of pages to scrape for paginated sites
     */
    private Integer maxPagesToScrape;
    /**
     * In case of static recruiter code for config, according to recruiterProvider
     * (default behavior, see {@link com.erhgo.services.externaloffer.recruiterdispatcher.PerOfferATSConfigurationItem} )
     */
    private String recruiterCode;
    /**
     * In case offers list is not directly at the root of remote object, path to offers list
     */
    private String rootPath;
    /**
     * Global code for ats
     */
    private String atsCode;
    /**
     * Code for one specific configuration for this ats - typically for one of our customers.
     */
    private String configCode;
    /**
     * Bean name to use to compute our recruiter code, based on config & offer
     */
    private String recruiterProvider;
    /**
     * Bean name to use to compute max number of offers to create per external offers fetch for this config
     */
    private String limiterServiceName;
    /**
     * Override default Slack channel used to send sum up after this config has been executed
     */
    private String forcedSlackChannel;
    /**
     * In case offer fetch requires basic auth
     */
    private String basicAuthentication;
    /**
     * Custom parameters ; may includes other auth stuff
     */
    private List<String> customHeaders = new ArrayList<>();
    /**
     * In case offer fetch is paginated
     */
    private String pageIndexParam;
    private String pageSizeParam;
    private String totalCountParam;
    // Some ATS do not send more than a given number of offers
    private Integer globalMaxNumberOfOffersAllowed;
    /**
     * In case of a dedicated request is required to generate a token
     */
    private Authentication tokenAuthentication;
    private Integer pageSize;
    private Boolean requiresConfirmation;
    /**
     * Service called on offer creation, when additional requests are required to have fulfilled offer
     */
    private String lazyInitServiceName;
    /**
     * URL for offer lazy initialization
     */
    private String lazyInitUrl;

    /**
     * Filter string that must be present in job URLs scrapped
     */
    private String offerUrlMustContain;
    /**
     * Filter string that must not be present in job URLs scrapped
     */
    private String offerUrlMustNotContain;

    /**
     * True if ATS is sync once a day, false if twice
     */
    private Boolean isSinglePassPerDay;

    public static AtsGetOfferConfig buildForManualOffer(ExternalOffer offer) {
        return new AtsGetOfferConfig()
                .setRequiresConfirmation(true)
                .setAtsCode(offer.getAtsCode())
                .setConfigCode(offer.getConfigCode())
                .setRecruiterCode(offer.getComputedRecruiterCode());
    }

    public String getAtsAndConfigCode() {
        return formatForConfigCode(configCode);
    }

    public Boolean getRequiresConfirmation() {
        return BooleanUtils.isTrue(requiresConfirmation);
    }

    public String formatForConfigCode(String configCode) {
        return "%s--%s".formatted(atsCode, StringUtils.trimToEmpty(configCode));
    }

    @Override
    public AtsGetOfferConfig clone() {
        try {
            return (AtsGetOfferConfig) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new GenericTechnicalException("clone unsupported", e);
        }
    }

    public boolean isAuthenticated() {
        return StringUtils.isNotBlank(basicAuthentication);
    }

    public boolean isPaginated() {
        return StringUtils.isNoneBlank(pageIndexParam, pageSizeParam);
    }

    public List<String> getCustomHeaders() {
        var allHeaders = new ArrayList<String>();
        if (customHeaders != null) {
            allHeaders.addAll(customHeaders);
        }
        return allHeaders;
    }

    public boolean requiresTokenGeneration() {
        return tokenAuthentication != null;
    }


}
